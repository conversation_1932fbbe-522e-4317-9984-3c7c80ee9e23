# Nobify

Nobify is a comprehensive personal crypto management platform designed to help users track and manage their airdrop opportunities, portfolio investments, and important crypto notifications. Built with modern web technologies, it provides an intuitive dashboard for crypto enthusiasts to organize their holdings, monitor performance, and stay informed about key market events.

## 🤖 AI-Powered Development

This project showcases the power of **AI-assisted coding** and **vibe-driven development**. Nobify was built using modern AI coding tools and methodologies, demonstrating how artificial intelligence can accelerate development workflows while maintaining code quality and architectural integrity. The development process leverages AI for:

- **Code Generation:** Rapid prototyping and feature implementation
- **Architecture Planning:** AI-assisted system design and technical decisions
- **Documentation:** Automated generation of comprehensive project documentation
- **Testing Strategy:** AI-guided test planning and implementation
- **Optimization:** Performance improvements and code refactoring suggestions

This approach represents the future of software development, where human creativity and AI capabilities combine to create robust, scalable applications efficiently.

## 🚀 Features

### 📊 Nobify Dashboard
- **Centralized Overview:** Real-time display of portfolio value, recent transactions, and key metrics
- **Performance Analytics:** Track your crypto journey with comprehensive statistics
- **Quick Actions:** Fast access to all major features from a single hub

### 🎁 Nobify Airdrops
- **Comprehensive Tracking:** Monitor upcoming, active, completed, and missed airdrops
- **Eligibility Management:** Track your eligibility status for each airdrop opportunity
- **Smart Filtering:** Filter by status, eligibility, and deadline proximity
- **Claim Management:** Toggle claim status and track your airdrop rewards
- **Deadline Alerts:** Never miss an airdrop with automated reminders

### 💼 Nobify Portfolio
- **Multi-Wallet Support:** Manage crypto investments across multiple wallets
- **Real-Time Data:** Live price updates integrated with CoinGecko and CoinMarketCap APIs
- **Transaction History:** Detailed tracking of all buy/sell transactions
- **Performance Charts:** Visual analytics powered by Recharts
- **Holdings Analysis:** Current amounts, average costs, and profit/loss calculations

### 🔔 Nobify Alerts
- **Price Notifications:** Custom alerts for token price changes and thresholds
- **Airdrop Reminders:** Automated notifications for upcoming deadlines
- **Multi-Channel Delivery:** Push notifications via Firebase and email via SendGrid
- **Customizable Settings:** Personalize notification preferences and frequency

## 🏗️ Technical Architecture

### Frontend Stack
- **Framework:** Next.js 13+ with TypeScript
- **Styling:** Tailwind CSS for responsive design
- **Data Visualization:** Recharts for interactive charts and graphs
- **State Management:** React hooks and context API
- **UI Components:** Custom component library with dark/light theme support

### Backend Stack
- **Framework:** Fastify with TypeScript for high-performance API
- **Database:** PostgreSQL with Prisma ORM for type-safe database operations
- **Authentication:** JWT-based authentication with role-based access control
- **External APIs:** 
  - CoinGecko API for cryptocurrency data
  - CoinMarketCap API for market information
- **Real-time Updates:** Automated price fetching and portfolio synchronization

### Infrastructure & Services
- **Notifications:** 
  - Firebase Cloud Messaging for push notifications
  - SendGrid for email alerts and communications
- **Database Migrations:** Prisma migrations for schema management
- **API Documentation:** Comprehensive endpoint documentation
- **Error Handling:** Centralized error management and logging

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- PostgreSQL database
- Firebase project (for notifications)
- SendGrid account (for emails)
- CoinGecko/CoinMarketCap API keys

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/Nobhokleng/Nobify.git
cd Nobify
```

2. **Backend Setup**
```bash
cd backend
npm install
```

3. **Frontend Setup**
```bash
cd frontend
npm install
```

4. **Environment Configuration**

Create `.env` files in both backend and frontend directories:

**Backend `.env`:**
```env
DATABASE_URL="postgresql://username:password@localhost:5432/nobify"
JWT_SECRET="your-jwt-secret"
COINGECKO_API_KEY="your-coingecko-api-key"
COINMARKETCAP_API_KEY="your-coinmarketcap-api-key"
SENDGRID_API_KEY="your-sendgrid-api-key"
FIREBASE_PROJECT_ID="your-firebase-project-id"
```

**Frontend `.env.local`:**
```env
NEXT_PUBLIC_API_URL="http://localhost:3001"
NEXT_PUBLIC_FIREBASE_CONFIG="your-firebase-config"
```

5. **Database Setup**
```bash
cd backend
npx prisma migrate dev
npx prisma generate
```

6. **Start Development Servers**

Backend:
```bash
cd backend
npm run dev
```

Frontend:
```bash
cd frontend
npm run dev
```

Visit `http://localhost:3000` to access the application.

## 📱 Screenshots

### Nobify Dashboard
![Nobify Dashboard](assets/nobify_dashbaord.png)
*Comprehensive overview of your crypto activities and portfolio performance*

### Nobify Airdrops
![Nobify Airdrops](assets/nobify_airdrop.png)
*Track and manage all your airdrop opportunities in one place*

### Nobify Portfolio
![Nobify Portfolio](assets/nobify_portfolio.png)
*Detailed portfolio analysis with real-time data and performance metrics*

### Nobify Alerts
![Nobify Alerts and Notifications](assets/nobify_alerts.png)
*Customizable notifications to never miss important crypto events*

## 🗺️ Roadmap

### Phase 1: Research & Foundation ✅
- [x] Market research and competitive analysis
- [x] Brand identity and design system
- [x] Technical architecture planning

### Phase 2: Core Development ✅
- [x] User authentication and authorization
- [x] Portfolio management system
- [x] Airdrop tracking functionality
- [x] Real-time price integration
- [x] Notification system

### Phase 3: Testing & Launch 🚧
- [ ] Comprehensive testing suite
- [ ] Performance optimization
- [ ] Security audit
- [ ] Beta user testing
- [ ] Production deployment

### Phase 4: Enhancement & Growth 📋
- [ ] Mobile application
- [ ] Advanced analytics and insights
- [ ] Social features and community
- [ ] DeFi protocol integrations
- [ ] Multi-language support

## 🤝 Contributing

We welcome contributions to Nobify! Please read our contributing guidelines and submit pull requests for any improvements.

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support, email <EMAIL> or join our community discussions.

## 🔗 Links

- [Product Requirements Document](scripts/nobify_prd.txt)
- [API Documentation](docs/api.md)
- [Contributing Guidelines](CONTRIBUTING.md)

---

**Built with ❤️ by the Nobify Team**

*Empowering crypto enthusiasts with better portfolio management and airdrop tracking.*